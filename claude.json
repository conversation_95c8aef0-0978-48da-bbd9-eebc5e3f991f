{"numStartups": 2, "installMethod": "unknown", "autoUpdates": true, "tipsHistory": {"new-user-warmup": 1}, "projects": {"/home/<USER>/claude_code": {"allowedTools": [], "history": [{"display": "What tools do I have available?", "pastedContents": {}}, {"display": "/init ", "pastedContents": {}}], "mcpContextUris": [], "mcpServers": {"n8n-automation": {"url": "http://localhost:3000/mcp", "secret": "H9c5SYrqUNCjKQUMxofkfHsqZpfh48Ef"}}, "enabledMcpjsonServers": [], "disabledMcpjsonServers": [], "hasTrustDialogAccepted": true, "projectOnboardingSeenCount": 0, "hasClaudeMdExternalIncludesApproved": false, "hasClaudeMdExternalIncludesWarningShown": false, "lastTotalWebSearchRequests": 0, "hasCompletedProjectOnboarding": true}}, "firstStartTime": "2025-07-11T16:38:49.073Z", "userID": "1ea28f35e0dd18833dabc8bafc1b949c7fa492c3da29df7bd9252fe20ce6e998", "oauthAccount": {"accountUuid": "30aaf901-9803-4136-bcfa-10750d1d3d43", "emailAddress": "<EMAIL>", "organizationUuid": "0a61f4f1-ff0a-4271-855a-b606fddea5d6", "organizationRole": "admin", "workspaceRole": null, "organizationName": "<EMAIL>'s Organization"}, "hasCompletedOnboarding": true, "lastOnboardingVersion": "1.0.48", "subscriptionNoticeCount": 0, "hasAvailableSubscription": false, "cachedChangelog": "# Changelog\n\n## 1.0.48\n\n- Fixed a bug in v1.0.45 where the app would sometimes freeze", "changelogLastFetched": *************, "fallbackAvailableWarningThreshold": 0.5, "lastReleaseNotesSeen": "1.0.48"}